<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.file.name || "File Content" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div
    class="flex-1 overflow-y-auto mt-18 mb-20 px-6 pt-6 pb-[3px] flex flex-col space-x-4"
  >
    <div class="relative flex flex-col items-center h-full">
      <button
        class="absolute top-4 right-4 z-2 flex items-center justify-center !w-[46.5px] !h-[46.5px] rounded-xl bg-base-400 dark:bg-dark-base-400 border border-primary-border dark:border-dark-primary-border cursor-pointer hover:opacity-70"
        (click)="copyContent()"
      >
        <app-svg-icon
          dxPrefix
          type="icCopy"
          class="text-2xl w-6 h-6 !text-primary dark:!text-dark-primary"
        ></app-svg-icon>
      </button>
      <dx-form-field class="w-full h-full">
        <textarea
        dxInput
        [disabled]="!isEditingContent()"
        [(ngModel)]="fileContentValue"
        rows="10"
        placeholder="No content available"
        class="!h-full !overflow-y-auto !resize-none"
      ></textarea>
      </dx-form-field>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    @if (isEditingContent()) {
    <button
      dxLoadingButton="filled"
      [loading]="isSaving()"
      (click)="saveFileContent()"
    >
      Save and Retrain
    </button>
    } @else {
    <button dxButton="filled" (click)="toggleEditContent()">
      <div class="flex items-center">
<!--        <app-svg-icon type="icEdit" class="w-6 h-6 !text-white"></app-svg-icon>-->
        <span>Edit</span>
      </div>
    </button>
    }
  </div>
</div>
